// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "@openzeppelin/contracts/proxy/transparent/ProxyAdmin.sol";
import "@openzeppelin/contracts/proxy/utils/UUPSUpgradeable.sol";
import {IPCoin} from "../src/IPCoin.sol";

contract UpgradeToken is Script {
    function setUp() public {}

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        // address proxyAddress = vm.envAddress("TOKEN_ADDRESS");

        vm.startBroadcast(deployerPrivateKey);

        // Note: IPCoin is no longer upgradeable, this script needs to be redesigned
        // Deploy a new IPCoin instance instead of upgrading
        IPCoin newIPCoin = new IPCoin("New IP Coin", "NIPC", msg.sender);
        console.log("New IPCoin deployed at:", address(newIPCoin));
        console.log("Warning: IPCoin is no longer upgradeable. Consider migration instead.");

        console.log("IPCoin upgraded to new implementation");

        vm.stopBroadcast();
    }
}
