// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {IPCoin} from "../src/IPCoin.sol";

contract UpgradePool is Script {
    function setUp() public {}

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");

        vm.startBroadcast(deployerPrivateKey);

        // Deploy IPCoin directly (no proxy needed for non-upgradeable contract)
        IPCoin ipCoin = new IPCoin("NAME1", "N1", msg.sender);
        console.log("IPCoin deployed at:", address(ipCoin));

        console.log("IPCoin deployment completed");

        vm.stopBroadcast();
    }
}
