// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {IPCoin} from "../src/IPCoin.sol";
import {TraderNft} from "../src/TraderNft.sol";
import {CreatorNft} from "../src/CreatorNft.sol";
import {Governance} from "../src/Governance.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {Factory} from "../src/Factory.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract DeployFactory is Script {
    function setUp() public {}

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address usdtAddress = vm.envAddress("USDT_ADDRESS");
        address poolAddress = vm.envAddress("POOL_PROXY_ADDRESS");
        address govAddress = vm.envAddress("GOVERNANCE_PROXY_ADDRESS");
        address feeCollector = vm.envAddress("FEE_COLLECTOR_ADDRESS");

        vm.startBroadcast(deployerPrivateKey);

        // Deploy implementation contracts
        IPCoin ipCoinImpl = new IPCoin("Factory IP Coin", "FIPC", msg.sender);
        TraderNft traderNftImpl = new TraderNft();
        CreatorNft creatorNftImpl = new CreatorNft();

        // Deploy Factory implementation
        Factory factoryImpl = new Factory();

        // Prepare initialization data for Factory
        bytes memory initData = abi.encodeWithSelector(
            Factory.initialize.selector,
            IERC20(usdtAddress),
            feeCollector,
            address(ipCoinImpl),
            address(traderNftImpl),
            address(creatorNftImpl),
            govAddress,
            poolAddress
        );

        // Deploy Factory proxy
        ERC1967Proxy factoryProxy = new ERC1967Proxy(address(factoryImpl), initData);

        // The factoryProxy address is now the address of our upgradeable Factory contract
        Factory factory = Factory(address(factoryProxy));

        console.log("Factory Proxy deployed at:", address(factory));

        console.log("Factory Implementation:", address(factoryImpl));
        console.log("IPCoin implementation:", address(ipCoinImpl));
        console.log("TraderNft implementation:", address(traderNftImpl));
        console.log("CreatorNft implementation:", address(creatorNftImpl));
        console.log("Governance address:", govAddress);
        console.log("DistributionPool address:", poolAddress);

        vm.stopBroadcast();
    }
}
