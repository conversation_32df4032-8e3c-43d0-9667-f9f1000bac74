// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {IPCoin} from "../src/IPCoin.sol";
import {TraderNft} from "../src/TraderNft.sol";
import {CreatorNft} from "../src/CreatorNft.sol";
import {Governance} from "../src/Governance.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {MockUSDT} from "../test/MockUSDT.sol";

contract DeployLogic is Script {
    function setUp() public {}

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        string memory outputFile = vm.envString("LOGIC_OUTPUT_FILE");

        vm.startBroadcast(deployerPrivateKey);

        IPCoin ipCoin = new IPCoin("TestCoin", "TEST", msg.sender);
        CreatorNft creatorNft = new CreatorNft();
        // TraderNft traderNft = new TraderNft();
        // Governance governance = new Governance();
        // DistributionPool distributionPool = new DistributionPool();

        vm.stopBroadcast();

        // 将地址写入环境变量输出文件，供后续脚本使用
        string memory ipCoinAddr = vm.toString(address(ipCoin));
        string memory creatorNftAddr = vm.toString(address(creatorNft));

        string memory output =
            string.concat("IPCOIN_LOGIC_ADDRESS=", ipCoinAddr, "\n", "CREATOR_NFT_LOGIC_ADDRESS=", creatorNftAddr, "\n");

        vm.writeFile(outputFile, output);

        console.log("IPCoin logic deployed at:", address(ipCoin));
        console.log("CreatorNft logic deployed at:", address(creatorNft));
        console.log("Logic contract addresses written to:", outputFile);
    }
}
