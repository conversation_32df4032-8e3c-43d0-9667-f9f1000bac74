// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../src/Governance.sol";
import "../src/IPCoin.sol";
import "../src/TraderNft.sol";
import "../src/CreatorNft.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract GovernanceTest is Test {
    Governance public governance;
    address public governanceProxy;
    IPCoin public coin;
    TraderNft public traderNft;
    CreatorNft public creatorNft;
    address public owner;
    address public user1;
    address public user2;
    address public user3;
    address public user4;

    function setUp() public {
        console.log("Starting setUp");
        owner = address(this);
        user1 = address(0x1);
        user2 = address(0x2);
        user3 = address(0x3);
        user4 = address(0x4);

        console.log("Deploying IPCoin");
        // IPCoin is now non-upgradeable, deploy directly
        coin = new IPCoin("IP Coin", "IPC", governanceProxy);

        console.log("Deploying TraderNft");
        TraderNft traderNftImpl = new TraderNft();
        address traderNftProxy = address(new ERC1967Proxy(address(traderNftImpl), ""));
        traderNft = TraderNft(traderNftProxy);

        console.log("Deploying CreatorNft");
        CreatorNft creatorNftImpl = new CreatorNft();
        address creatorNftProxy = address(new ERC1967Proxy(address(creatorNftImpl), ""));
        creatorNft = CreatorNft(creatorNftProxy);

        console.log("Deploying Governance implementation");
        Governance governanceImpl = new Governance();
        console.log("Deploying Governance proxy");
        governanceProxy = address(new ERC1967Proxy(address(governanceImpl), ""));
        console.log("Governance proxy deployed at", governanceProxy);
        governance = Governance(governanceProxy);

        governance.initialize(owner);
        governance.addIp(address(coin), address(traderNft));
        governance.setOpenGovernance(true);

        // IPCoin is already initialized in constructor, no need for separate initialization
        traderNft.initialize(owner, owner, "Trader NFT", "TNFT", "https://example.com/");
        // fixme: use pool
        creatorNft.initialize(owner, user4, address(0), "Creator NFT", "CNFT", "https://example.com/creator");

        console.log("Setting up permissions and minting NFTs");
        vm.startPrank(owner);
        traderNft.safeMint(user1, 100);
        traderNft.safeMint(user2, 100);
        traderNft.safeMint(user3, 100);
        vm.stopPrank();

        console.log("setUp completed");
    }

    function testCreateProposal() public {
        vm.prank(user1);
        governance.createProposal(address(coin), 1000 * 1e18, "Test Proposal");

        (address creator, uint256 amount,,, string memory description, bool executed) =
            governance.getProposal(address(coin), 1);

        assertEq(creator, user1, "Creator should be user1");
        assertEq(amount, 1000 * 1e18, "Amount should be 1000 tokens");
        assertEq(description, "Test Proposal", "Description should match");
        assertEq(executed, false, "Proposal should not be executed yet");
    }

    function testVoteAndExecuteProposal() public {
        vm.prank(user1);
        governance.createProposal(address(coin), 1000 * 1e18, "Test Proposal");

        vm.prank(user1);
        governance.vote(address(coin), 1);

        vm.prank(user2);
        governance.vote(address(coin), 1);

        vm.prank(user3);
        governance.vote(address(coin), 1);

        (,,,,, bool executed) = governance.getProposal(address(coin), 1);

        assertEq(executed, true, "Proposal should be executed after reaching threshold");
        assertEq(coin.balanceOf(user1), 1000 * 1e18, "Creator should receive minted tokens");
    }

    function testVoterFee() public {
        governance.changeVoterFee(address(coin), 1000); // 10% fee

        vm.prank(user1);
        governance.createProposal(address(coin), 1000 * 1e18, "Test Proposal with Fee");

        vm.prank(user1);
        governance.vote(address(coin), 1);

        vm.prank(user2);
        governance.vote(address(coin), 1);

        vm.prank(user3);
        governance.vote(address(coin), 1);

        assertEq(coin.balanceOf(user1), 1100 * 1e18, "Creator should receive minted tokens plus fee");
        assertEq(coin.balanceOf(user2), 100 * 1e18, "Voter should receive fee");
        assertEq(coin.balanceOf(user3), 100 * 1e18, "Voter should receive fee");
    }

    function testOnlyTraderCanVote() public {
        vm.prank(user1);
        governance.createProposal(address(coin), 1000 * 1e18, "Test Proposal");

        address nonTrader = address(0x4);
        vm.expectRevert("Must hold Trader NFT to vote");
        vm.prank(nonTrader);
        governance.vote(address(coin), 1);
    }

    function testCannotVoteTwice() public {
        vm.prank(user1);
        governance.createProposal(address(coin), 1000 * 1e18, "Test Proposal");

        vm.prank(user1);
        governance.vote(address(coin), 1);

        vm.expectRevert("Already voted");
        vm.prank(user1);
        governance.vote(address(coin), 1);
    }
}
