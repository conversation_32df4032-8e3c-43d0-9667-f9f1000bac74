// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../src/DistributionPool.sol";
import "../src/IPCoin.sol";
import "../src/CreatorNft.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract MiningExhaustionTest is Test {
    DistributionPool public pool;
    IPCoin public coin;
    CreatorNft public creatorNft;

    address public owner = address(0x1);
    address public feeReceiver = address(0x2);
    address public creator = address(0x3);
    address public aiWallet = address(0x4);
    address public distributor = address(0x5);

    function setUp() public {
        vm.startPrank(owner);

        // Deploy contracts
        DistributionPool poolImpl = new DistributionPool();
        address poolProxy = address(new ERC1967Proxy(address(poolImpl), ""));
        pool = DistributionPool(payable(poolProxy));

        CreatorNft creatorNftImpl = new CreatorNft();
        address creatorNftProxy = address(new ERC1967Proxy(address(creatorNftImpl), ""));
        creatorNft = CreatorNft(creatorNftProxy);

        // Deploy IPCoin directly (non-upgradeable) - removed owner parameter
        coin = new IPCoin("IP Coin", "IPC", address(pool));

        // Initialize other contracts

        pool.initialize(owner, feeReceiver, owner);

        creatorNft.initialize(owner, creator, address(pool), "Creator NFT", "CNFT", "https://example.com/");

        // Add IP
        pool.addIp(address(coin), address(creatorNft), aiWallet);

        // Add distributor to whitelist
        pool.addRewardDistributor(distributor);

        vm.stopPrank();
    }

    function testMiningExhaustionScenario() public {
        // Get initial mining parameters
        uint256 totalSupply = pool.TOTAL_MINING_SUPPLY();

        console.log("Starting mining exhaustion test");
        console.log("Total mining supply:", totalSupply);

        // Simulate many weeks of mining to approach exhaustion
        uint256 weekCount = 0;
        uint256 cumulativeDistribution = 0;

        // Fast forward through many weeks
        while (weekCount < 600) {
            // Advance time first to ensure we pass the interval check (except for first iteration)
            if (weekCount > 0) {
                vm.warp(block.timestamp + 1 weeks + 1);
            }

            // Simulate 600 weeks (about 11.5 years)
            // Get current pool data
            (,,,,,,,,,,,,,,, uint256 remainingSupply,,,) = pool.coinPoolsData(address(coin));

            if (remainingSupply == 0) {
                console.log("Mining exhausted at week:", weekCount);
                break;
            }

            // Get current week number and calculate proper weekly output limit
            uint256 currentWeekNumber = pool.getCurrentWeekNumber(address(coin));
            uint256 maxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), currentWeekNumber);

            // Ensure we don't exceed remaining supply
            if (maxWeeklyOutput > remainingSupply) {
                maxWeeklyOutput = remainingSupply;
            }

            // If the reward is too small (less than 1 token), consider mining exhausted
            if (maxWeeklyOutput < 1e18) {
                console.log("Mining effectively exhausted at week:", weekCount);
                console.log("Current week number:", currentWeekNumber);
                console.log("Remaining supply:", remainingSupply);
                console.log("Max weekly output:", maxWeeklyOutput);
                break;
            }

            // Distribute the maximum allowed amount
            vm.prank(distributor);
            pool.distributeCreatorRewards(address(coin), maxWeeklyOutput);

            cumulativeDistribution += maxWeeklyOutput;
            weekCount++;

            // Log progress every 50 weeks
            if (weekCount % 50 == 0) {
                console.log("Week:", weekCount);
                console.log("Current week number:", currentWeekNumber);
                console.log("Cumulative distribution:", cumulativeDistribution);
                console.log("Remaining supply after distribution:", remainingSupply - maxWeeklyOutput);
            }
        }

        // Get final state
        (,,,,,,,,,,,,,, uint256 finalCumulative, uint256 finalRemaining, uint256 finalWeek,,) =
            pool.coinPoolsData(address(coin));

        console.log("Final week:", finalWeek);
        console.log("Final cumulative distribution:", finalCumulative);
        console.log("Final remaining supply:", finalRemaining);

        // Verify mining lasted a reasonable amount of time
        assertGt(weekCount, 400, "Mining should last for many weeks");

        // Verify most of the supply was distributed
        uint256 distributionPercentage = (finalCumulative * 100) / totalSupply;
        assertGt(distributionPercentage, 95, "Should distribute most of the supply");

        console.log("Distribution percentage:", distributionPercentage);
    }

    function testCannotRestartAfterExhaustion() public {
        // Test the mining distribution mechanism over multiple weeks

        // First, do a normal distribution to initialize the mining
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), 1000 * 1e18);

        // Test that we can continue distributing over multiple weeks
        uint256 maxIterations = 10; // Test just a few weeks to verify the mechanism

        for (uint256 i = 0; i < maxIterations; i++) {
            // Advance time to pass interval check
            vm.warp(block.timestamp + 1 weeks + 1);

            // Get current pool data
            (,,,,,,,,,,,,,,, uint256 remainingSupply,,,) = pool.coinPoolsData(address(coin));

            if (remainingSupply == 0) {
                console.log("Mining exhausted at iteration:", i);
                break;
            }

            // Get current week number and calculate proper weekly output limit
            uint256 currentWeekNumber = pool.getCurrentWeekNumber(address(coin));
            uint256 maxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), currentWeekNumber);

            // Ensure we don't exceed remaining supply
            if (maxWeeklyOutput > remainingSupply) {
                maxWeeklyOutput = remainingSupply;
            }

            // If the reward is too small, break
            if (maxWeeklyOutput < 1e18) {
                console.log("Mining effectively exhausted at iteration:", i);
                console.log("Remaining supply:", remainingSupply);
                break;
            }

            // Distribute the maximum allowed amount
            vm.prank(distributor);
            pool.distributeCreatorRewards(address(coin), maxWeeklyOutput);

            console.log("Week", i + 2);
            console.log("Distributed:", maxWeeklyOutput);
            console.log("Remaining:", remainingSupply - maxWeeklyOutput);
        }

        // Test that the mechanism works correctly
        (,,,,,,,,,,,,,,, uint256 finalRemainingSupply,,,) = pool.coinPoolsData(address(coin));
        console.log("Final remaining supply:", finalRemainingSupply);

        // Verify that we can still distribute if there's remaining supply
        if (finalRemainingSupply > 0) {
            uint256 currentWeekNumber = pool.getCurrentWeekNumber(address(coin));
            uint256 maxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), currentWeekNumber);

            console.log("Max weekly output for next week:", maxWeeklyOutput);

            // Test that we can distribute a reasonable amount
            uint256 testAmount = maxWeeklyOutput > finalRemainingSupply ? finalRemainingSupply : maxWeeklyOutput;
            if (testAmount > 1e18) {
                testAmount = 1e18; // Just test with 1 token
            }

            if (testAmount > 0) {
                vm.warp(block.timestamp + 1 weeks + 1);
                vm.prank(distributor);
                pool.distributeCreatorRewards(address(coin), testAmount);
                console.log("Successfully distributed test amount:", testAmount);
            }
        }

        console.log("Mining distribution mechanism test completed");
    }

    function testSimpleExhaustionPrevention() public {
        // Test the basic logic: time interval limits and weekly production rate limits

        // First distribution to initialize
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), 1000 * 1e18);

        // Get current state
        (,,,,,,,,,,,,,,, uint256 remaining,,,) = pool.coinPoolsData(address(coin));

        console.log("Initial state - Remaining:", remaining);

        // Test 1: Cannot distribute again in the same interval
        vm.prank(distributor);
        vm.expectRevert("Time interval limit: one distribution per interval");
        pool.distributeCreatorRewards(address(coin), 1000 * 1e18);

        // Advance time to pass the interval
        vm.warp(block.timestamp + 1 weeks + 1);

        // Test 2: Verify that we cannot distribute more than the maximum allowed
        uint256 currentWeekNumber = pool.getCurrentWeekNumber(address(coin));
        uint256 maxAllowed = pool.getWeeklyOutputLimit(address(coin), currentWeekNumber);

        console.log("Current week number:", currentWeekNumber);
        console.log("Max allowed for this week:", maxAllowed);

        // Try to distribute more than allowed - should fail
        vm.prank(distributor);
        vm.expectRevert("Exceeds maximum weekly production rate");
        pool.distributeCreatorRewards(address(coin), maxAllowed + 1e18);

        // Distribute the maximum allowed amount
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), maxAllowed);

        console.log("Successfully distributed maximum allowed amount");
    }
}
