// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../src/DistributionPool.sol";
import "../src/IPCoin.sol";
import "../src/CreatorNft.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract MiningExhaustionTest is Test {
    DistributionPool public pool;
    IPCoin public coin;
    CreatorNft public creatorNft;

    address public owner = address(0x1);
    address public feeReceiver = address(0x2);
    address public creator = address(0x3);
    address public aiWallet = address(0x4);
    address public distributor = address(0x5);

    function setUp() public {
        vm.startPrank(owner);

        // Deploy contracts
        DistributionPool poolImpl = new DistributionPool();
        address poolProxy = address(new ERC1967Proxy(address(poolImpl), ""));
        pool = DistributionPool(payable(poolProxy));

        CreatorNft creatorNftImpl = new CreatorNft();
        address creatorNftProxy = address(new ERC1967Proxy(address(creatorNftImpl), ""));
        creatorNft = CreatorNft(creatorNftProxy);

        // Deploy IPCoin directly (non-upgradeable) - removed owner parameter
        coin = new IPCoin("IP Coin", "IPC", address(pool));

        // Initialize other contracts

        pool.initialize(owner, feeReceiver, owner);

        creatorNft.initialize(owner, creator, address(pool), "Creator NFT", "CNFT", "https://example.com/");

        // Add IP
        pool.addIp(address(coin), address(creatorNft), aiWallet);

        // Add distributor to whitelist
        pool.addRewardDistributor(distributor);

        vm.stopPrank();
    }

    function testMiningExhaustionScenario() public {
        // Get initial mining parameters
        uint256 totalSupply = pool.TOTAL_MINING_SUPPLY();
        uint256 decayConstant = pool.DECAY_CONSTANT();
        uint256 decayPrecision = pool.DECAY_PRECISION();

        console.log("Starting mining exhaustion test");
        console.log("Total mining supply:", totalSupply);

        // Simulate many weeks of mining to approach exhaustion
        uint256 weekCount = 0;
        uint256 cumulativeDistribution = 0;

        // Fast forward through many weeks
        while (weekCount < 600) {
            // Simulate 600 weeks (about 11.5 years)
            // Get current pool data
            (,,,,,,,,,,,,,,, uint256 remainingSupply,,,) = pool.coinPoolsData(address(coin));

            if (remainingSupply == 0) {
                console.log("Mining exhausted at week:", weekCount);
                break;
            }

            // Calculate maximum weekly output
            uint256 maxWeeklyOutput = (remainingSupply * decayConstant) / decayPrecision;

            // If the reward is too small (less than 1 token), consider mining exhausted
            if (maxWeeklyOutput < 1e18) {
                console.log("Mining effectively exhausted at week:", weekCount);
                console.log("Remaining supply:", remainingSupply);
                console.log("Max weekly output:", maxWeeklyOutput);
                break;
            }

            // Distribute the maximum allowed amount
            vm.prank(distributor);
            pool.distributeCreatorRewards(address(coin), maxWeeklyOutput);

            cumulativeDistribution += maxWeeklyOutput;
            weekCount++;

            // Advance time by 1 week
            vm.warp(block.timestamp + 1 weeks);

            // Log progress every 50 weeks
            if (weekCount % 50 == 0) {
                console.log("Week:", weekCount);
                console.log("Cumulative distribution:", cumulativeDistribution);
                console.log("Remaining supply:", remainingSupply - maxWeeklyOutput);
            }
        }

        // Get final state
        (,,,,,,,,,,,,,, uint256 finalCumulative, uint256 finalRemaining, uint256 finalWeek,,) =
            pool.coinPoolsData(address(coin));

        console.log("Final week:", finalWeek);
        console.log("Final cumulative distribution:", finalCumulative);
        console.log("Final remaining supply:", finalRemaining);

        // Verify mining lasted a reasonable amount of time
        assertGt(weekCount, 400, "Mining should last for many weeks");

        // Verify most of the supply was distributed
        uint256 distributionPercentage = (finalCumulative * 100) / totalSupply;
        assertGt(distributionPercentage, 95, "Should distribute most of the supply");

        console.log("Distribution percentage:", distributionPercentage);
    }

    function testCannotRestartAfterExhaustion() public {
        // Manually set the pool to an exhausted state
        // This simulates what would happen after many years of mining

        // First, do a normal distribution to initialize the mining
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), 1000 * 1e18);

        // Get the pool data storage slot and manually set remaining supply to 0
        // while keeping weekNumber > 0 to simulate exhaustion
        vm.store(
            address(pool),
            keccak256(abi.encode(address(coin), uint256(76))), // coinPoolsData mapping slot
            bytes32(0) // This will set the entire struct to 0, but we need to be more precise
        );

        // Actually, let's use a different approach - simulate exhaustion by distributing everything
        uint256 totalSupply = pool.TOTAL_MINING_SUPPLY();

        // Reset and start fresh
        vm.prank(owner);
        pool.addIp(address(coin), address(creatorNft), aiWallet);

        // Distribute almost everything in chunks
        uint256 remaining = totalSupply;
        uint256 weekNum = 0;

        while (remaining > 1e18 && weekNum < 100) {
            // Limit to prevent infinite loop
            uint256 toDistribute = remaining > 10_000_000 * 1e18 ? 10_000_000 * 1e18 : remaining;

            // Check if we can still distribute
            try pool.distributeCreatorRewards(address(coin), toDistribute) {
                remaining -= toDistribute;
                weekNum++;
                vm.warp(block.timestamp + 1 weeks);
            } catch {
                break;
            }

            vm.prank(distributor);
        }

        // Now try to distribute when supply is exhausted
        vm.prank(distributor);
        vm.expectRevert("Mining supply exhausted");
        pool.distributeCreatorRewards(address(coin), 1e18);

        console.log("Successfully prevented distribution after exhaustion");
    }

    function testSimpleExhaustionPrevention() public {
        // Test the basic logic: once remainingSupply is 0 and weekNumber > 0,
        // no new mining should be allowed

        // First distribution to initialize
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), 1000 * 1e18);

        // Get current state
        (,,,,,,,,,,,,,,, uint256 remaining, uint256 week,,) = pool.coinPoolsData(address(coin));

        console.log("Initial state - Remaining:", remaining, "Week:", week);

        // Verify that we cannot distribute more than the maximum allowed
        uint256 maxAllowed = (remaining * pool.DECAY_CONSTANT()) / pool.DECAY_PRECISION();

        vm.warp(block.timestamp + 1 weeks);

        // Try to distribute more than allowed - should fail
        vm.prank(distributor);
        vm.expectRevert("Exceeds maximum weekly production rate");
        pool.distributeCreatorRewards(address(coin), maxAllowed + 1);

        // Distribute the maximum allowed amount
        vm.prank(distributor);
        pool.distributeCreatorRewards(address(coin), maxAllowed);

        console.log("Successfully distributed maximum allowed amount");
    }
}
