// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../src/IPCoin.sol";
import "../src/DistributionPool.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "forge-std/Test.sol";
import "forge-std/console.sol";

contract IPCoinTest is Test {
    IPCoin public coin;
    address public coinProxy;
    DistributionPool public pool;
    address public poolProxy;
    address public owner;
    address public user1;
    address public user2;
    address public minter1;
    address public minter2;

    function setUp() public {
        owner = address(this);
        user1 = address(0x1);
        user2 = address(0x2);
        minter1 = address(0x3);
        minter2 = address(0x4);

        // Deploy pool first as it's needed for IPCoin initialization
        DistributionPool poolImpl = new DistributionPool();
        poolProxy = address(new ERC1967Proxy(address(poolImpl), ""));
        pool = DistributionPool(payable(poolProxy));
        pool.initialize(owner, address(0x5), owner);

        // Deploy IPCoin directly (non-upgradeable) - removed owner parameter
        coin = new IPCoin("IP Coin", "IPC", minter1);
        coinProxy = address(coin);
    }

    function testInitialization() public view {
        assertEq(coin.name(), "IP Coin", "Name should match");
        assertEq(coin.symbol(), "IPC", "Symbol should match");
        assertEq(coin.minter(), minter1, "Minter should be set correctly");
        // Note: New IPCoin only supports single minter set during construction
        // Owner functionality has been removed from IPCoin
    }

    function testMinting() public {
        vm.startPrank(minter1);
        coin.mint(user1, 1000);
        assertEq(coin.balanceOf(user1), 1000, "Balance should be 1000");
        vm.stopPrank();

        // Note: Only minter1 can mint, minter2 cannot mint
        // This test now only tests the designated minter
    }

    function test_RevertWhen_MintingByNonMinter() public {
        vm.expectRevert();
        vm.prank(user1);
        coin.mint(user2, 1000);
    }

    function test_RevertWhen_MintingZeroAmount() public {
        // Note: ERC20 _mint doesn't revert on zero amount, it just does nothing
        // This test is removed as zero amount minting is actually allowed in ERC20
        vm.startPrank(minter1);
        coin.mint(user1, 0);
        assertEq(coin.balanceOf(user1), 0, "Balance should remain 0");
        assertTrue(coin.mintingDisabled(), "Minting should be disabled even after zero mint");
        vm.stopPrank();
    }

    function testMintingDisabled() public {
        // Test that minting works initially
        vm.startPrank(minter1);
        coin.mint(user1, 1000);
        assertEq(coin.balanceOf(user1), 1000, "Initial minting should work");

        // Test that minting is disabled after first mint
        assertTrue(coin.mintingDisabled(), "Minting should be disabled after first mint");

        // Test that subsequent minting fails
        vm.expectRevert("Minting is disabled");
        coin.mint(user1, 1000);
        vm.stopPrank();
    }

    function testMinterManagement() public {
        // Note: New IPCoin only supports single minter set during construction
        // Test that the minter is correctly set
        assertEq(coin.minter(), minter1, "Minter should be set correctly");

        // Test that only the designated minter can mint
        vm.startPrank(minter1);
        coin.mint(user1, 1000);
        assertEq(coin.balanceOf(user1), 1000, "Minting should work for designated minter");
        vm.stopPrank();

        // Test that non-minter cannot mint
        vm.startPrank(minter2);
        vm.expectRevert("Caller is not the minter");
        coin.mint(user1, 1000);
        vm.stopPrank();
    }

    function test_RevertWhen_NonMinterTryToMint() public {
        // Test that non-minter cannot mint
        vm.startPrank(user1);
        vm.expectRevert("Caller is not the minter");
        coin.mint(user1, 1000);
        vm.stopPrank();

        // Test that minter2 (not the designated minter) cannot mint
        vm.startPrank(minter2);
        vm.expectRevert("Caller is not the minter");
        coin.mint(user1, 1000);
        vm.stopPrank();
    }

    function testBasicFunctionality() public {
        // Note: New IPCoin is not upgradeable and has no pause functionality
        // Test that the contract works as expected without upgrade functionality

        // Test basic functionality
        vm.startPrank(minter1);
        coin.mint(user1, 1000);
        vm.stopPrank();

        assertEq(coin.balanceOf(user1), 1000, "Minting should work");
        assertTrue(coin.mintingDisabled(), "Minting should be disabled after first mint");

        // Test that subsequent minting fails due to minting being disabled
        vm.startPrank(minter1);
        vm.expectRevert("Minting is disabled");
        coin.mint(user1, 500);
        vm.stopPrank();

        // Test that non-minter cannot mint
        vm.startPrank(user1);
        vm.expectRevert("Caller is not the minter");
        coin.mint(user1, 500);
        vm.stopPrank();
    }

    function testPairInitialization() public {
        address testPair = address(0x999);

        // Test that only minter can initialize pair
        vm.startPrank(user1);
        vm.expectRevert("Caller is not the minter");
        coin.initPair(testPair);
        vm.stopPrank();

        // Test successful pair initialization with event
        vm.startPrank(minter1);
        vm.expectEmit(true, false, false, false);
        emit IPCoin.PairInitialized(testPair);
        coin.initPair(testPair);
        vm.stopPrank();

        assertEq(coin.pair(), testPair, "Pair should be set correctly");
    }

    function testListedStatusChange() public {
        // Initially not listed
        assertFalse(coin.listed(), "Should not be listed initially");

        // Test that only minter can set listed status
        vm.startPrank(user1);
        vm.expectRevert("Caller is not the minter");
        coin.setListed();
        vm.stopPrank();

        // Test successful listing with event
        vm.startPrank(minter1);
        vm.expectEmit(true, false, false, false);
        emit IPCoin.ListedStatusChanged(true);
        coin.setListed();
        vm.stopPrank();

        assertTrue(coin.listed(), "Should be listed after setListed call");
    }
}
