// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract IPCoin is ERC20 {
    event PairInitialized(address indexed pair);
    event ListedStatusChanged(bool isListed);
    error TokenNotListed();

    bool public mintingDisabled;
    bool public listed;

    address public pair;
    address public immutable minter;

    constructor(
        string memory name_,
        string memory symbol_,
        address _minter
    ) ERC20(name_, symbol_){
        require(_minter != address(0), "Minter cannot be zero address");
        minter = _minter;
    }

    modifier onlyMinter() {
        require(msg.sender == minter, "Caller is not the minter");
        _;
    }

    modifier onlyMintingEnable() {
        require(!mintingDisabled, "Minting is disabled");
        _;
    }

    function mint(address to, uint256 amount) public onlyMinter onlyMintingEnable {
        _mint(to, amount);
        disableMinting();
    }

    function initPair(
        address pair_
    ) external onlyMinter {
        pair = pair_;
        emit PairInitialized(pair_);
    }

    function setListed() external onlyMinter {
        listed = true;
        emit ListedStatusChanged(true);
    }

    function disableMinting() internal {
        mintingDisabled = true;
    }

}
