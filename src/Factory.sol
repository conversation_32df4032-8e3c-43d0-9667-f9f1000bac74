// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./IPCoin.sol";
import "./TraderNft.sol";
import "./CreatorNft.sol";
import "./DistributionPool.sol";

/**
 * @title Factory Contract
 * @notice This is a factory contract used to deploy and manage IP-related contracts
 * @dev This contract is responsible for deploying IPCoin, TraderNft, and CreatorNft contracts, and managing their implementation addresses
 */
contract Factory is Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    // Event triggered when a new IP contract is deployed
    event IPDeployed(address indexed creator, string ipRef, address ipCoin, address traderNft, address creatorNft);

    // Event triggered when minimum buy value is updated
    event MinimumBuyValueUpdated(uint256 oldValue, uint256 newValue);

    error TraderNftNotDeployed();
    error MinimumTransactionLimit();
    error ZeroAddress();

    DistributionPool public distributionPool;

    address public admin;

    // Store implementation addresses for each contract
    address public traderNftImpl;
    address public creatorNftImpl;

    uint256 public minimumBuyValue;

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the contract
     * @param admin_ Administrator address
     * @param creatorNftImpl_ Creator NFT implementation contract address
     * @param distributionPool_ Distribution pool contract address
     */
    function initialize(address admin_, address creatorNftImpl_, address distributionPool_) public initializer {
        if (admin_ == address(0)) revert ZeroAddress();
        if (creatorNftImpl_ == address(0)) revert ZeroAddress();
        if (distributionPool_ == address(0)) revert ZeroAddress();

        __Ownable_init(admin_);
        admin = admin_;

        creatorNftImpl = creatorNftImpl_;

        distributionPool = DistributionPool(payable(distributionPool_));

        minimumBuyValue = 0 ether;
    }

    /**
     * @notice Deployment parameters structure without trader NFT
     * @param coinName Token name
     * @param coinSymbol Token symbol
     * @param creator Creator address
     * @param aiAgentWallet AI Agent wallet address
     * @param creatorNftName Creator NFT name
     * @param creatorNftSymbol Creator NFT symbol
     * @param creatorNftURI Creator NFT URI
     * @param ipRef IP reference, used by backend to capture this value for business processing
     */
    struct DeploymentWithOutTraderNftParams {
        string coinName;
        string coinSymbol;
        address creator;
        address aiAgentWallet;
        string creatorNftName;
        string creatorNftSymbol;
        string creatorNftURI;
        string ipRef;
    }

    /**
     * @notice Complete deployment parameters structure
     * @param coinName Token name
     * @param coinSymbol Token symbol
     * @param traderNftName Trader NFT name
     * @param traderNftSymbol Trader NFT symbol
     * @param traderNftBaseURI Trader NFT base URI
     * @param creator Creator address
     * @param aiAgentWallet AI Agent wallet address
     * @param creatorNftName Creator NFT name
     * @param creatorNftSymbol Creator NFT symbol
     * @param creatorNftURI Creator NFT URI
     * @param ipRef IP reference, used by backend to capture this value for business processing
     */
    struct DeploymentParams {
        string coinName;
        string coinSymbol;
        string traderNftName;
        string traderNftSymbol;
        string traderNftBaseURI;
        address creator;
        address aiAgentWallet;
        string creatorNftName;
        string creatorNftSymbol;
        string creatorNftURI;
        string ipRef;
    }

    /**
     * @notice Deploy complete IP-related contract suite (including trader NFT)
     * @param params Deployment parameters
     */
    function deployIP(
        DeploymentParams memory params
    ) external payable {
        if (traderNftImpl == address(0)) {
            revert TraderNftNotDeployed();
        }
        require(msg.value >= minimumBuyValue, MinimumTransactionLimit());

        // Deploy IPCoin directly
        IPCoin ipCoinInstance = new IPCoin(params.coinName, params.coinSymbol, address(distributionPool));
        address ipCoinAddress = address(ipCoinInstance);

        // Deploy proxy contracts
        address traderNftProxyAddress = newERC1967Proxy(traderNftImpl);
        address creatorNftProxyAddress = newERC1967Proxy(creatorNftImpl);

        CreatorNft creatorNftInstance = CreatorNft(creatorNftProxyAddress);

        DeploymentWithOutTraderNftParams memory p = DeploymentWithOutTraderNftParams({
            coinName: params.coinName,
            coinSymbol: params.coinSymbol,
            creator: params.creator,
            aiAgentWallet: params.aiAgentWallet,
            creatorNftName: params.creatorNftName,
            creatorNftSymbol: params.creatorNftSymbol,
            creatorNftURI: params.creatorNftURI,
            ipRef: params.ipRef
        });

        // _initializeContracts will initialize CreatorNft and add to DistributionPool.
        // IPCoin initialization is handled by its constructor.
        _initializeContracts(ipCoinInstance, creatorNftInstance, p);

        // Initialize TraderNft
        TraderNft traderNftInstance = TraderNft(traderNftProxyAddress);
        traderNftInstance.initialize(
            admin,
            address(distributionPool),
            params.traderNftName,
            params.traderNftSymbol,
            "" // Base URI
        );

        emit IPDeployed(params.creator, params.ipRef, ipCoinAddress, traderNftProxyAddress, creatorNftProxyAddress);
    }

    /**
     * @notice Deploy IP-related contracts without trader NFT
     * @param params Deployment parameters
     */
    function deployIPWithoutTraderNft(
        DeploymentWithOutTraderNftParams memory params
    ) external payable nonReentrant returns (address) {
        require(msg.value >= minimumBuyValue, MinimumTransactionLimit());

        // Deploy IPCoin directly (non-upgradeable)
        IPCoin ipCoinInstance = new IPCoin(params.coinName, params.coinSymbol, address(distributionPool));
        address ipCoinAddress = address(ipCoinInstance);

        // CreatorNft is still deployed as a proxy
        address creatorNftProxyAddress = newERC1967Proxy(creatorNftImpl);
        CreatorNft creatorNftInstance = CreatorNft(creatorNftProxyAddress);

        // Call _initializeContracts, passing the IPCoin instance.
        // The _initializeContracts function will no longer call ipCoin.initialize() for the non-upgradeable IPCoin.
        _initializeContracts(ipCoinInstance, creatorNftInstance, params);

        emit IPDeployed(params.creator, params.ipRef, ipCoinAddress, address(0), creatorNftProxyAddress);

        return ipCoinAddress;
    }

    /**
     * @notice Initialize IP token and creator NFT contracts
     * @param ipCoin IP token contract instance
     * @param creatorNft Creator NFT contract instance
     * @param params Deployment parameters
     */
    function _initializeContracts(
        IPCoin ipCoin,
        CreatorNft creatorNft,
        DeploymentWithOutTraderNftParams memory params
    ) internal {
        // IPCoin initialization logic (previously done by ipCoin.initialize)
        // is now handled by the IPCoin constructor as it's non-upgradeable.
        // Thus, the call to ipCoin.initialize() is removed here.

        creatorNft.initialize(
            admin,
            params.creator,
            address(distributionPool),
            params.creatorNftName,
            params.creatorNftSymbol,
            params.creatorNftURI
        );

        distributionPool.addIp(address(ipCoin), address(creatorNft), params.aiAgentWallet);
        if (msg.value > 0) {
            distributionPool.buy{value: msg.value}(address(ipCoin), params.creator, 0);
        }
    }

    /**
     * @notice Create new ERC1967 proxy contract
     * @param logicAddress_ Logic contract address
     * @return Proxy contract address
     */
    function newERC1967Proxy(
        address logicAddress_
    ) public returns (address) {
        return address(new ERC1967Proxy(logicAddress_, ""));
    }

    /**
     * @notice Set new implementation contract addresses
     * @param traderNftImpl_ New trader NFT implementation contract address
     * @param creatorNftImpl_ New creator NFT implementation contract address
     */
    function setImplementations(address traderNftImpl_, address creatorNftImpl_) external onlyOwner {
        if (traderNftImpl_ != address(0)) {
            traderNftImpl = traderNftImpl_;
        }
        if (creatorNftImpl_ != address(0)) {
            creatorNftImpl = creatorNftImpl_;
        }
    }

    /**
     * @notice Set new owner address
     * @param owner_ New owner address
     */
    function setOwner(
        address owner_
    ) external onlyOwner {
        if (owner_ == address(0)) revert ZeroAddress();
        transferOwnership(owner_);
        admin = owner_;
    }

    /**
     * @notice Set new minimum buy value
     * @param minimumBuyValue_ New minimum buy value
     */
    function setMinimumBuyValue(
        uint256 minimumBuyValue_
    ) external onlyOwner {
        uint256 oldValue = minimumBuyValue;
        minimumBuyValue = minimumBuyValue_;
        emit MinimumBuyValueUpdated(oldValue, minimumBuyValue_);
    }

    /**
     * @notice Update distribution pool contract address
     * @param distributionPool_ New distribution pool contract address
     */
    function updateDistributionPool(
        address distributionPool_
    ) external onlyOwner {
        distributionPool = DistributionPool(payable(distributionPool_));
    }

    /**
     * @notice Authorize upgrade function
     * @dev Only contract owner can call
     * @param newImplementation New implementation contract address
     */
    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyOwner {}
}
